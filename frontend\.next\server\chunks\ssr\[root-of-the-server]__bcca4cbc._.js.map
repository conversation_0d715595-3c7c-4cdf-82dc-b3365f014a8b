{"version": 3, "sources": [], "sections": [{"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/context/userContext.js"], "sourcesContent": ["import axios from \"axios\";\nimport { usePathname, useRouter } from \"next/navigation\";\nimport React, { useEffect, useState, useContext, useCallback } from \"react\";\nimport toast from \"react-hot-toast\";\n\nconst UserContext = React.createContext();\n\n// set axios to include credentials with every request\naxios.defaults.withCredentials = true;\n\nexport const UserContextProvider = ({ children }) => {\n  const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL;\n\n  const router = useRouter();\n\n  const pathname = usePathname();\n\n  const [user, setUser] = useState({});\n  const [allUsers, setAllUsers] = useState([]);\n  const [userState, setUserState] = useState({\n    name: \"\",\n    email: \"\",\n    password: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n\n  // register user\n  const registerUser = async (e) => {\n    e.preventDefault();\n    if (\n      !userState.email.includes(\"@\") ||\n      !userState.password ||\n      userState.password.length < 6\n    ) {\n      toast.error(\"Please enter a valid email and password (min 6 characters)\");\n      return;\n    }\n\n    try {\n      const res = await axios.post(`${serverUrl}/api/v1/register`, userState);\n      toast.success(res.data.message);\n\n      // clear the form\n      setUserState({\n        name: \"\",\n        email: \"\",\n        password: \"\",\n      });\n\n      // const verificationToken = res.data.verificationToken;\n      // if (verificationToken) {\n      //   router.push(`/verify-email/${verificationToken}`);\n      // }\n    } catch (error) {\n      console.log(\"Error registering user\", error);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  // login the user\n  const loginUser = async (e) => {\n    e.preventDefault();\n    try {\n      const res = await axios.post(\n        `${serverUrl}/api/v1/login`,\n        {\n          email: userState.email,\n          password: userState.password,\n        },\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      toast.success(res.data.message);\n\n      // clear the form\n      setUserState({\n        email: \"\",\n        password: \"\",\n      });\n\n      // refresh the user details\n      await getUser(); // fetch before redirecting\n\n      // push user to the dashboard page\n      router.push(\"/\");\n    } catch (error) {\n      console.log(\"Error logging in user\", error);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  // get user Looged in Status\n  const userLoginStatus = useCallback(async () => {\n    let loggedIn = false;\n    try {\n      const res = await axios.get(`${serverUrl}/api/v1/login-status`, {\n        withCredentials: true, // send cookies to the server\n      });\n\n      // coerce the string to boolean\n      loggedIn = !!res.data;\n      setLoading(false);\n\n      if (!loggedIn) {\n        router.push(\"/login\");\n      }\n    } catch (error) {\n      console.log(\"Error getting user login status\", error);\n    }\n\n    return loggedIn;\n  }, [serverUrl, router]);\n\n  // logout user\n  const logoutUser = async () => {\n    try {\n      const res = await axios.get(`${serverUrl}/api/v1/logout`, {\n        withCredentials: true, // send cookies to the server\n      });\n\n      toast.success(res.data.message);\n\n      if (\n        pathname === \"/favourites\" ||\n        pathname === \"/mysnippets\" ||\n        pathname === \"/profile\"\n      ) {\n        router.push(\"/login\");\n      }\n    } catch (error) {\n      console.log(\"Error logging out user\", error);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  // get user details\n  const getUser = async () => {\n    setLoading(true);\n    try {\n      const res = await axios.get(`${serverUrl}/api/v1/user`, {\n        withCredentials: true, // send cookies to the server\n      });\n\n      setUser((prevState) => {\n        return {\n          ...prevState,\n          ...res.data,\n        };\n      });\n\n      setLoading(false);\n    } catch (error) {\n      console.log(\"Error getting user details\", error);\n      setLoading(false);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  //get user by it's id\n  const getUserById = async (id) => {\n    setLoading(true);\n    try {\n      const user = await axios.get(`${serverUrl}/api/v1/user/${id}`, {\n        withCredentials: true,\n      });\n      setLoading(false);\n      return user.data;\n    } catch (error) {\n      console.log(\"error in fetching the user by id\", error);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  // get user activity\n  const getUserActivity = async (id) => {\n    setLoading(true);\n    try {\n      const res = await axios.get(`${serverUrl}/api/v1/user/${id}/activity`, {\n        withCredentials: true,\n      });\n      setLoading(false);\n      return res.data;\n    } catch (error) {\n      console.log(\"Error fetching user activity\", error);\n      setLoading(false);\n      toast.error(error.response?.data?.message || \"Failed to fetch activity\");\n      return [];\n    }\n  };\n\n  // update user details\n  const updateUser = async (e, data) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const res = await axios.patch(\n        `${serverUrl}/api/v1/update-profile`,\n        data,\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      // update the user state\n      setUser((prevState) => {\n        return {\n          ...prevState,\n          ...res.data,\n        };\n      });\n\n      toast.success(res.data.message);\n\n      setLoading(false);\n    } catch (error) {\n      console.log(\"Error updating user details\", error);\n      setLoading(false);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  // email verification\n  const emailVerification = async () => {\n    setLoading(true);\n    try {\n      const res = await axios.post(\n        `${serverUrl}/api/v1/verify-email`,\n        {},\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n      setLoading(false);\n      toast.success(res.data.message);\n    } catch (error) {\n      console.log(\"Error sending email verification\", error);\n      setLoading(false);\n      toast.error(error.response.data.message);\n    }\n  };\n\n  // verify user/email\n  const verifyUser = async (token) => {\n    setLoading(true);\n    try {\n      const res = await axios.post(\n        `${serverUrl}/api/v1/verify-user/${token}`,\n        {},\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      toast.success(res.data.message);\n\n      // refresh the user details\n      getUser();\n\n      setLoading(false);\n      // redirect to home page\n      router.push(\"/\");\n    } catch (error) {\n      console.log(\"Error verifying user\", error);\n      toast.error(error.response.data.message);\n      setLoading(false);\n    }\n  };\n\n  // forgot password email\n  const forgotPasswordEmail = async (email) => {\n    setLoading(true);\n\n    try {\n      const res = await axios.post(\n        `${serverUrl}/api/v1/forgot-password`,\n        {\n          email,\n        },\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      toast.success(res.data.message);\n      setLoading(false);\n    } catch (error) {\n      console.log(\"Error sending forgot password email\", error);\n      toast.error(error.response.data.message);\n      setLoading(false);\n    }\n  };\n\n  // reset password\n  const resetPassword = async (token, password) => {\n    setLoading(true);\n\n    try {\n      const res = await axios.post(\n        `${serverUrl}/api/v1/reset-password/${token}`,\n        {\n          password,\n        },\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      toast.success(res.data.message);\n      setLoading(false);\n      // redirect to login page\n      router.push(\"/login\");\n    } catch (error) {\n      console.log(\"Error resetting password\", error);\n      toast.error(error.response.data.message);\n      setLoading(false);\n    }\n  };\n\n  // change password\n  const changePassword = async (currentPassword, newPassword) => {\n    setLoading(true);\n\n    try {\n      await axios.patch(\n        `${serverUrl}/api/v1/change-password`,\n        { currentPassword, newPassword },\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      toast.success(\"Password changed successfully\");\n      setLoading(false);\n    } catch (error) {\n      console.log(\"Error changing password\", error);\n      toast.error(error.response.data.message);\n      setLoading(false);\n    }\n  };\n\n  // admin routes\n  const getAllUsers = async () => {\n    setLoading(true);\n    try {\n      const res = await axios.get(\n        `${serverUrl}/api/v1/admin/users`,\n        {},\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      setAllUsers(res.data);\n      setLoading(false);\n    } catch (error) {\n      console.log(\"Error getting all users\", error);\n      toast.error(error.response.data.message);\n      setLoading(false);\n    }\n  };\n\n  // dynamic form handler\n  const handlerUserInput = (name) => (e) => {\n    const value = e.target.value;\n\n    setUserState((prevState) => ({\n      ...prevState,\n      [name]: value,\n    }));\n  };\n\n  // delete user\n  const deleteUser = async (id) => {\n    setLoading(true);\n    try {\n      await axios.delete(\n        `${serverUrl}/api/v1/admin/users/${id}`,\n        {},\n        {\n          withCredentials: true, // send cookies to the server\n        }\n      );\n\n      toast.success(\"User deleted successfully\");\n      setLoading(false);\n      // refresh the users list\n      getAllUsers();\n    } catch (error) {\n      console.log(\"Error deleting user\", error);\n      toast.error(error.response.data.message);\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    const loginStatusGetUser = async () => {\n      const isLoggedIn = await userLoginStatus();\n\n      if (isLoggedIn) {\n        await getUser();\n      }\n    };\n\n    loginStatusGetUser();\n  }, [userLoginStatus]);\n\n  useEffect(() => {\n    if (user.role === \"admin\") {\n      getAllUsers();\n    }\n  }, [user.role]);\n\n  return (\n    <UserContext.Provider\n      value={{\n        registerUser,\n        userState,\n        handlerUserInput,\n        loginUser,\n        logoutUser,\n        userLoginStatus,\n        user,\n        getUserById,\n        updateUser,\n        emailVerification,\n        verifyUser,\n        forgotPasswordEmail,\n        resetPassword,\n        changePassword,\n        allUsers,\n        deleteUser,\n        getUserActivity,\n        loading,\n      }}\n    >\n      {children}\n    </UserContext.Provider>\n  );\n};\n\nexport const useUserContext = () => {\n  return useContext(UserContext);\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,aAAa;AAEvC,sDAAsD;AACtD,qIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,eAAe,GAAG;AAE1B,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAAE;IAC9C,MAAM;IAEN,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IACE,CAAC,UAAU,KAAK,CAAC,QAAQ,CAAC,QAC1B,CAAC,UAAU,QAAQ,IACnB,UAAU,QAAQ,CAAC,MAAM,GAAG,GAC5B;YACA,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,UAAU,gBAAgB,CAAC,EAAE;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAE9B,iBAAiB;YACjB,aAAa;gBACX,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;QAEA,wDAAwD;QACxD,2BAA2B;QAC3B,uDAAuD;QACvD,IAAI;QACN,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,iBAAiB;IACjB,MAAM,YAAY,OAAO;QACvB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC1B,GAAG,UAAU,aAAa,CAAC,EAC3B;gBACE,OAAO,UAAU,KAAK;gBACtB,UAAU,UAAU,QAAQ;YAC9B,GACA;gBACE,iBAAiB;YACnB;YAGF,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAE9B,iBAAiB;YACjB,aAAa;gBACX,OAAO;gBACP,UAAU;YACZ;YAEA,2BAA2B;YAC3B,MAAM,WAAW,2BAA2B;YAE5C,kCAAkC;YAClC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,yBAAyB;YACrC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,WAAW;QACf,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,oBAAoB,CAAC,EAAE;gBAC9D,iBAAiB;YACnB;YAEA,+BAA+B;YAC/B,WAAW,CAAC,CAAC,IAAI,IAAI;YACrB,WAAW;YAEX,IAAI,CAAC,UAAU;gBACb,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,mCAAmC;QACjD;QAEA,OAAO;IACT,GAAG;QAAC;QAAW;KAAO;IAEtB,cAAc;IACd,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,cAAc,CAAC,EAAE;gBACxD,iBAAiB;YACnB;YAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAE9B,IACE,aAAa,iBACb,aAAa,iBACb,aAAa,YACb;gBACA,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,mBAAmB;IACnB,MAAM,UAAU;QACd,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,YAAY,CAAC,EAAE;gBACtD,iBAAiB;YACnB;YAEA,QAAQ,CAAC;gBACP,OAAO;oBACL,GAAG,SAAS;oBACZ,GAAG,IAAI,IAAI;gBACb;YACF;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,WAAW;YACX,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,IAAI;YACF,MAAM,OAAO,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,aAAa,EAAE,IAAI,EAAE;gBAC7D,iBAAiB;YACnB;YACA,WAAW;YACX,OAAO,KAAK,IAAI;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oCAAoC;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,OAAO;QAC7B,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,aAAa,EAAE,GAAG,SAAS,CAAC,EAAE;gBACrE,iBAAiB;YACnB;YACA,WAAW;YACX,OAAO,IAAI,IAAI;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,WAAW;YACX,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC7C,OAAO,EAAE;QACX;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa,OAAO,GAAG;QAC3B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,KAAK,CAC3B,GAAG,UAAU,sBAAsB,CAAC,EACpC,MACA;gBACE,iBAAiB;YACnB;YAGF,wBAAwB;YACxB,QAAQ,CAAC;gBACP,OAAO;oBACL,GAAG,SAAS;oBACZ,GAAG,IAAI,IAAI;gBACb;YACF;YAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAE9B,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,WAAW;YACX,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC1B,GAAG,UAAU,oBAAoB,CAAC,EAClC,CAAC,GACD;gBACE,iBAAiB;YACnB;YAEF,WAAW;YACX,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oCAAoC;YAChD,WAAW;YACX,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,OAAO;QACxB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC1B,GAAG,UAAU,oBAAoB,EAAE,OAAO,EAC1C,CAAC,GACD;gBACE,iBAAiB;YACnB;YAGF,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAE9B,2BAA2B;YAC3B;YAEA,WAAW;YACX,wBAAwB;YACxB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,wBAAwB;YACpC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvC,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,OAAO;QACjC,WAAW;QAEX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC1B,GAAG,UAAU,uBAAuB,CAAC,EACrC;gBACE;YACF,GACA;gBACE,iBAAiB;YACnB;YAGF,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAC9B,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,uCAAuC;YACnD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvC,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,OAAO,OAAO;QAClC,WAAW;QAEX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC1B,GAAG,UAAU,uBAAuB,EAAE,OAAO,EAC7C;gBACE;YACF,GACA;gBACE,iBAAiB;YACnB;YAGF,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAC9B,WAAW;YACX,yBAAyB;YACzB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,4BAA4B;YACxC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvC,WAAW;QACb;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,OAAO,iBAAiB;QAC7C,WAAW;QAEX,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,KAAK,CACf,GAAG,UAAU,uBAAuB,CAAC,EACrC;gBAAE;gBAAiB;YAAY,GAC/B;gBACE,iBAAiB;YACnB;YAGF,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,2BAA2B;YACvC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvC,WAAW;QACb;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CACzB,GAAG,UAAU,mBAAmB,CAAC,EACjC,CAAC,GACD;gBACE,iBAAiB;YACnB;YAGF,YAAY,IAAI,IAAI;YACpB,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,2BAA2B;YACvC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvC,WAAW;QACb;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC,OAAS,CAAC;YAClC,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;YAE5B,aAAa,CAAC,YAAc,CAAC;oBAC3B,GAAG,SAAS;oBACZ,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IAEA,cAAc;IACd,MAAM,aAAa,OAAO;QACxB,WAAW;QACX,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAChB,GAAG,UAAU,oBAAoB,EAAE,IAAI,EACvC,CAAC,GACD;gBACE,iBAAiB;YACnB;YAGF,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,WAAW;YACX,yBAAyB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,uBAAuB;YACnC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YACvC,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,MAAM,aAAa,MAAM;YAEzB,IAAI,YAAY;gBACd,MAAM;YACR;QACF;QAEA;IACF,GAAG;QAAC;KAAgB;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB;QACF;IACF,GAAG;QAAC,KAAK,IAAI;KAAC;IAEd,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,iBAAiB;IAC5B,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/context/globalContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState } from \"react\";\r\n\r\nconst GlobalContext = createContext();\r\n\r\nexport const GlobalProvider = ({ children }) => {\r\n  const [modalMode, setModalMode] = useState(\"\");\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(true);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [activeSnippet, setActiveSnippet] = useState(null);\r\n\r\n  const openModalForSnippet = () => {\r\n    setModalMode(\"add-snippet\");\r\n    setIsEditing(true);\r\n  };\r\n\r\n  const openEmailVerificationModal = () => {\r\n    setModalMode(\"email-verification\");\r\n    setIsEditing(false);\r\n  };\r\n\r\n  const openProfileModal = () => {\r\n    setModalMode(\"profile\");\r\n  };\r\n\r\n  const openModalForEdit = (snippet) => {\r\n    setActiveSnippet(snippet);\r\n    setModalMode(\"edit-snippet\");\r\n    setIsEditing(true);\r\n  };\r\n\r\n  const openModalForSearch = () => {\r\n    setModalMode(\"search\");\r\n  };\r\n\r\n  const closeModal = () => {\r\n    setModalMode(\"\");\r\n    setIsEditing(false);\r\n    setActiveSnippet(null);\r\n  };\r\n\r\n  return (\r\n    <GlobalContext.Provider\r\n      value={{\r\n        modalMode,\r\n        isSidebarOpen,\r\n        isEditing,\r\n        activeSnippet,\r\n        openModalForSnippet,\r\n        openProfileModal,\r\n        openModalForEdit,\r\n        openModalForSearch,\r\n        closeModal,\r\n        setIsSidebarOpen,\r\n        openEmailVerificationModal,\r\n      }}\r\n    >\r\n      {children}\r\n    </GlobalContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useGlobalContext = () => {\r\n  return useContext(GlobalContext);\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAE3B,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,sBAAsB;QAC1B,aAAa;QACb,aAAa;IACf;IAEA,MAAM,6BAA6B;QACjC,aAAa;QACb,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,aAAa;IACf;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;QACjB,aAAa;QACb,aAAa;IACf;IAEA,MAAM,qBAAqB;QACzB,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,aAAa;QACb,iBAAiB;IACnB;IAEA,qBACE,8OAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,mBAAmB;IAC9B,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/context/snippetContext.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useEffect,\r\n  useMemo,\r\n  useState,\r\n  useCallback,\r\n} from \"react\";\r\nimport toast from \"react-hot-toast\";\r\nimport { useGlobalContext } from \"./globalContext\";\r\nimport { useUserContext } from \"./userContext\";\r\n\r\nconst SnippetsContext = createContext();\r\n\r\nexport const SnippetsProvider = ({ children }) => {\r\n  const { closeModal } = useGlobalContext();\r\n  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL;\r\n  const serverUrl = `${baseUrl}/api/v1`;\r\n\r\n  const userId = useUserContext().user?._id;\r\n\r\n  const [publicSnippets, setPublicSnippets] = useState([]);\r\n  const [tags, setTags] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [userSnippets, setUserSnippets] = useState([]);\r\n  const [likedSnippets, setLikedSnippets] = useState([]);\r\n  const [leaderboard, setLeaderboard] = useState([]);\r\n  const [popularSnippets, setPopularSnippets] = useState([]);\r\n\r\n  const createSnippet = async (data) => {\r\n    try {\r\n      await axios.post(`${serverUrl}/create-snippet`, data);\r\n\r\n      getPublicSnippets();\r\n\r\n      toast.success(\"Snippet created successfully\");\r\n\r\n      closeModal();\r\n    } catch (error) {\r\n      console.log(\"Error creating snippet\", error);\r\n      toast.error(error.response.data.message);\r\n    }\r\n  };\r\n\r\n  const updateSnippet = async (data) => {\r\n    try {\r\n      await axios.patch(`${serverUrl}/snippet/${data._id}`, data);\r\n\r\n      getPublicSnippets();\r\n      getUserSnippets();\r\n      toast.success(\"Snippet updated successfully\");\r\n    } catch (error) {\r\n      console.log(\"Error updating snippet\", error);\r\n    }\r\n  };\r\n\r\n  const getPublicSnippets = useCallback(\r\n    async (userId, tagId, searchQuery, page) => {\r\n      setLoading(true);\r\n      try {\r\n        const queryParams = new URLSearchParams();\r\n\r\n        if (userId) {\r\n          queryParams.append(\"userId\", userId);\r\n        }\r\n\r\n        if (tagId) {\r\n          queryParams.append(\"tagId\", tagId);\r\n        }\r\n\r\n        if (searchQuery) {\r\n          queryParams.append(\"search\", searchQuery);\r\n        }\r\n\r\n        if (page) {\r\n          queryParams.append(\"page\", page);\r\n        }\r\n\r\n        const res = await axios.get(\r\n          `${serverUrl}/snippets/public?${queryParams.toString()}`\r\n        );\r\n\r\n        if (res.data && res.data.snippets) {\r\n          setPublicSnippets(res.data.snippets);\r\n          setLoading(false);\r\n          return res.data.snippets;\r\n        } else {\r\n          setPublicSnippets([]);\r\n          setLoading(false);\r\n          return [];\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Error fetching public snippets\", error);\r\n        setPublicSnippets([]);\r\n        setLoading(false);\r\n        return [];\r\n      }\r\n    },\r\n    [serverUrl]\r\n  );\r\n\r\n  const getUserSnippets = useCallback(\r\n    async (tagId, search) => {\r\n      // Only fetch user snippets if user is logged in\r\n      if (!userId) {\r\n        setUserSnippets([]);\r\n        return [];\r\n      }\r\n\r\n      setLoading(true);\r\n      try {\r\n        const queryParams = new URLSearchParams();\r\n\r\n        if (tagId) {\r\n          queryParams.append(\"tagId\", tagId);\r\n        }\r\n\r\n        if (search) {\r\n          queryParams.append(\"search\", search);\r\n        }\r\n\r\n        const res = await axios.get(\r\n          `${serverUrl}/snippets?${queryParams.toString()}`,\r\n          {\r\n            withCredentials: true,\r\n          }\r\n        );\r\n\r\n        setLoading(false);\r\n        setUserSnippets(res.data);\r\n\r\n        return res.data;\r\n      } catch (error) {\r\n        // Handle authentication errors silently\r\n        if (error.response?.status === 401 || error.response?.status === 404) {\r\n          setUserSnippets([]);\r\n          setLoading(false);\r\n          return [];\r\n        }\r\n        console.log(\"Error fetching user snippets\", error);\r\n        setUserSnippets([]);\r\n        setLoading(false);\r\n        return [];\r\n      }\r\n    },\r\n    [userId, serverUrl]\r\n  );\r\n\r\n  const getLikedSnippets = useCallback(\r\n    async (tagId, search) => {\r\n      // Only fetch liked snippets if user is logged in\r\n      if (!userId) {\r\n        setLikedSnippets([]);\r\n        return [];\r\n      }\r\n\r\n      setLoading(true);\r\n      try {\r\n        const queryParams = new URLSearchParams();\r\n\r\n        if (tagId) {\r\n          queryParams.append(\"tagId\", tagId);\r\n        }\r\n\r\n        if (search) {\r\n          queryParams.append(\"search\", search);\r\n        }\r\n\r\n        const res = await axios.get(\r\n          `${serverUrl}/snippets/liked?${queryParams.toString()}`,\r\n          {\r\n            withCredentials: true,\r\n          }\r\n        );\r\n\r\n        setLoading(false);\r\n        setLikedSnippets(res.data);\r\n        return res.data;\r\n      } catch (error) {\r\n        if (error.response?.status === 401 || error.response?.status === 404) {\r\n          setLikedSnippets([]);\r\n          setLoading(false);\r\n          return [];\r\n        }\r\n        console.log(\"Error fetching liked snippets\", error);\r\n        setLikedSnippets([]);\r\n        setLoading(false);\r\n        return [];\r\n      }\r\n    },\r\n    [userId, serverUrl]\r\n  );\r\n\r\n  const getPublicSnippetById = async (id) => {\r\n    setLoading(true);\r\n    try {\r\n      const res = await axios.get(`${serverUrl}/snippet/public/${id}`);\r\n\r\n      setLoading(false);\r\n      return res.data.snippet;\r\n    } catch (error) {\r\n      console.log(\"Error fetching snippet by id\", error);\r\n    }\r\n  };\r\n\r\n  const getTags = useCallback(async () => {\r\n    try {\r\n      const res = await axios.get(`${serverUrl}/tags`);\r\n      setTags(res.data.tags);\r\n    } catch (error) {\r\n      console.log(\"Error fetching tags\", error);\r\n    }\r\n  }, [serverUrl]);\r\n\r\n  const likeSnippet = async (id) => {\r\n    try {\r\n      const res = await axios.patch(\r\n        `${serverUrl}/snippet/like/${id}`,\r\n        {},\r\n        {\r\n          withCredentials: true,\r\n        }\r\n      );\r\n      if (res.data.message) {\r\n        toast.success(res.data.message);\r\n      }\r\n      return res.data;\r\n    } catch (error) {\r\n      console.log(\"Error liking snippet\", error);\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to update like status.\"\r\n      );\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Helper function to update snippet in arrays\r\n  const updateSnippetInArrays = (snippetId, updateFn) => {\r\n    // Update public snippets\r\n    setPublicSnippets((prevSnippets) =>\r\n      prevSnippets.map((snippet) =>\r\n        snippet._id === snippetId ? updateFn(snippet) : snippet\r\n      )\r\n    );\r\n\r\n    // Update user snippets\r\n    setUserSnippets((prevData) => ({\r\n      ...prevData,\r\n      snippets:\r\n        prevData.snippets?.map((snippet) =>\r\n          snippet._id === snippetId ? updateFn(snippet) : snippet\r\n        ) || [],\r\n    }));\r\n\r\n    // Update popular snippets\r\n    setPopularSnippets((prevData) => ({\r\n      ...prevData,\r\n      snippets:\r\n        prevData.snippets?.map((snippet) =>\r\n          snippet._id === snippetId ? updateFn(snippet) : snippet\r\n        ) || [],\r\n    }));\r\n\r\n    // Update liked snippets\r\n    setLikedSnippets((prevData) => ({\r\n      ...prevData,\r\n      snippets:\r\n        prevData.snippets?.map((snippet) =>\r\n          snippet._id === snippetId ? updateFn(snippet) : snippet\r\n        ) || [],\r\n    }));\r\n  };\r\n\r\n  const deleteSnippet = async (id) => {\r\n    try {\r\n      await axios.delete(`${serverUrl}/snippet/${id}`);\r\n      toast.success(\"Snippet deleted successfully\");\r\n      getPublicSnippets();\r\n      getUserSnippets();\r\n    } catch (error) {\r\n      console.log(\"Error deleting snippet\", error);\r\n      toast.error(error.response.data.message);\r\n    }\r\n  };\r\n\r\n  // get leaderboard\r\n  const getLeaderboard = useCallback(async () => {\r\n    setLoading(true);\r\n    try {\r\n      const res = await axios.get(`${serverUrl}/leaderboard`);\r\n\r\n      setLoading(false);\r\n      setLeaderboard(res.data);\r\n\r\n      return res.data;\r\n    } catch (error) {\r\n      console.log(\"Error fetching leaderboard\", error);\r\n    }\r\n  }, [serverUrl]);\r\n\r\n  // popular snippets\r\n  const getPopularSnippets = useCallback(\r\n    async (tagId, search) => {\r\n      setLoading(true);\r\n      try {\r\n        const queryParams = new URLSearchParams();\r\n\r\n        if (tagId) {\r\n          queryParams.append(\"tagId\", tagId);\r\n        }\r\n\r\n        if (search) {\r\n          queryParams.append(\"search\", search);\r\n        }\r\n\r\n        const res = await axios.get(\r\n          `${serverUrl}/snippets/popular?${queryParams.toString()}`\r\n        );\r\n\r\n        setLoading(false);\r\n\r\n        setPopularSnippets(res.data);\r\n\r\n        return res.data;\r\n      } catch (error) {\r\n        console.log(\"Error fetching popular snippets\", error);\r\n      }\r\n    },\r\n    [serverUrl]\r\n  );\r\n\r\n  const gradients = {\r\n    buttonGradient1:\r\n      \"linear-gradient(110.42deg, rgba(107, 190, 146, 0.1) 29.2%, rgba(245, 102, 146, 0.1) 63.56%)\",\r\n    buttonGradient2:\r\n      \"linear-gradient(110.42deg, rgba(25, 151, 222, 0.1) 29.2%, rgba(168, 85, 247, 0.1) 63.56%)\",\r\n    buttonGradient3:\r\n      \"linear-gradient(110.42deg, rgba(25, 151, 222, 0.1) 29.2%, rgba(168, 85, 247, 0.1) 63.56%)\",\r\n    buttonGradient4:\r\n      \"linear-gradient(110.42deg, rgba(168, 85, 247, 0.1) 29.2%, rgba(245, 102, 146, 0.1) 63.56%)\",\r\n    buttonGradient5:\r\n      \"linear-gradient(110.42deg, rgba(25, 151, 222, 0.1) 29.2%, rgba(168, 85, 247, 0.1) 63.56%)\",\r\n    buttonGradient6:\r\n      \"linear-gradient(110.42deg, rgba(25, 151, 222, 0.1) 29.2%, rgba(168, 85, 247, 0.1) 63.56%)\",\r\n    buttonGradient7:\r\n      \"linear-gradient(110.42deg, rgba(41, 25, 222, 0.1) 29.2%, rgba(235, 87, 87, 0.1) 63.56%)\",\r\n    buttonGradient8:\r\n      \"linear-gradient(110.42deg, rgba(25, 151, 222, 0.1) 29.2%, rgba(168, 85, 247, 0.1) 63.56%)\",\r\n    buttonGradient9:\r\n      \"linear-gradient(110.42deg, rgba(226, 195, 33, 0.1) 29.2%, rgba(247, 104, 85, 0.1) 63.56%)\",\r\n    buttonGradient10:\r\n      \"linear-gradient(110.42deg, rgba(235, 87, 87, 0.1) 29.2%, rgba(189, 68, 166, 0.1) 53.82%, rgba(247, 85, 143, 0.1) 63.56%)\",\r\n    buttonGradient11:\r\n      \"linear-gradient(110.42deg, rgba(25, 151, 222, 0.1) 29.2%, rgba(168, 85, 247, 0.1) 63.56%)\",\r\n    buttonGradient12:\r\n      \"linear-gradient(110.42deg, rgba(226, 195, 33, 0.1) 29.2%, rgba(247, 104, 85, 0.1) 63.56%)\",\r\n    buttonGradient13:\r\n      \"linear-gradient(110.42deg, rgba(226, 195, 33, 0.1) 29.2%, rgba(99, 3, 255, 0.1) 63.56%)\",\r\n    buttonGradient14:\r\n      \"linear-gradient(110.42deg, rgba(41, 25, 222, 0.1) 29.2%, rgba(235, 87, 87, 0.1) 63.56%)\",\r\n  };\r\n\r\n  const randomButtonColor =\r\n    Object.values(gradients)[\r\n      Math.floor(Math.random() * Object.values(gradients).length)\r\n    ];\r\n\r\n  const randomTagColor =\r\n    Object.values(gradients)[\r\n      Math.floor(Math.random() * Object.values(gradients).length)\r\n    ];\r\n\r\n  // memo\r\n  const useBtnColorMemo = useMemo(() => randomButtonColor, [randomButtonColor]);\r\n  const useTagColorMemo = useMemo(() => randomTagColor, [randomTagColor]);\r\n\r\n  useEffect(() => {\r\n    getPublicSnippets();\r\n    getTags();\r\n    getLeaderboard();\r\n    getPopularSnippets();\r\n    // Only fetch user-specific data if user is logged in\r\n    if (userId) {\r\n      getUserSnippets();\r\n      getLikedSnippets();\r\n    }\r\n  }, [\r\n    userId,\r\n    getPublicSnippets,\r\n    getTags,\r\n    getLeaderboard,\r\n    getPopularSnippets,\r\n    getUserSnippets,\r\n    getLikedSnippets,\r\n  ]);\r\n\r\n  return (\r\n    <SnippetsContext.Provider\r\n      value={{\r\n        publicSnippets,\r\n        getPublicSnippets,\r\n        useBtnColorMemo,\r\n        useTagColorMemo,\r\n        createSnippet,\r\n        tags,\r\n        updateSnippet,\r\n        deleteSnippet,\r\n        likeSnippet,\r\n        updateSnippetInArrays,\r\n        getPublicSnippetById,\r\n        loading,\r\n        getUserSnippets,\r\n        userSnippets,\r\n        getLikedSnippets,\r\n        likedSnippets,\r\n        getLeaderboard,\r\n        leaderboard,\r\n        getPopularSnippets,\r\n        popularSnippets,\r\n        getTags,\r\n      }}\r\n    >\r\n      {children}\r\n    </SnippetsContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useSnippetContext = () => {\r\n  return useContext(SnippetsContext);\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAQA;AACA;AACA;;;;;;;AAEA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAE7B,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAE;IAC3C,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IACtC,MAAM;IACN,MAAM,YAAY,GAAG,QAAQ,OAAO,CAAC;IAErC,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,IAAI,IAAI,EAAE;IAEtC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEzD,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,UAAU,eAAe,CAAC,EAAE;YAEhD;YAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAEd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,GAAG,UAAU,SAAS,EAAE,KAAK,GAAG,EAAE,EAAE;YAEtD;YACA;YACA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,0BAA0B;QACxC;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,OAAO,QAAQ,OAAO,aAAa;QACjC,WAAW;QACX,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ;gBACV,YAAY,MAAM,CAAC,UAAU;YAC/B;YAEA,IAAI,OAAO;gBACT,YAAY,MAAM,CAAC,SAAS;YAC9B;YAEA,IAAI,aAAa;gBACf,YAAY,MAAM,CAAC,UAAU;YAC/B;YAEA,IAAI,MAAM;gBACR,YAAY,MAAM,CAAC,QAAQ;YAC7B;YAEA,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CACzB,GAAG,UAAU,iBAAiB,EAAE,YAAY,QAAQ,IAAI;YAG1D,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjC,kBAAkB,IAAI,IAAI,CAAC,QAAQ;gBACnC,WAAW;gBACX,OAAO,IAAI,IAAI,CAAC,QAAQ;YAC1B,OAAO;gBACL,kBAAkB,EAAE;gBACpB,WAAW;gBACX,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,kBAAkB,EAAE;YACpB,WAAW;YACX,OAAO,EAAE;QACX;IACF,GACA;QAAC;KAAU;IAGb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,OAAO,OAAO;QACZ,gDAAgD;QAChD,IAAI,CAAC,QAAQ;YACX,gBAAgB,EAAE;YAClB,OAAO,EAAE;QACX;QAEA,WAAW;QACX,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,OAAO;gBACT,YAAY,MAAM,CAAC,SAAS;YAC9B;YAEA,IAAI,QAAQ;gBACV,YAAY,MAAM,CAAC,UAAU;YAC/B;YAEA,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CACzB,GAAG,UAAU,UAAU,EAAE,YAAY,QAAQ,IAAI,EACjD;gBACE,iBAAiB;YACnB;YAGF,WAAW;YACX,gBAAgB,IAAI,IAAI;YAExB,OAAO,IAAI,IAAI;QACjB,EAAE,OAAO,OAAO;YACd,wCAAwC;YACxC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,WAAW,KAAK;gBACpE,gBAAgB,EAAE;gBAClB,WAAW;gBACX,OAAO,EAAE;YACX;YACA,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,gBAAgB,EAAE;YAClB,WAAW;YACX,OAAO,EAAE;QACX;IACF,GACA;QAAC;QAAQ;KAAU;IAGrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,OAAO,OAAO;QACZ,iDAAiD;QACjD,IAAI,CAAC,QAAQ;YACX,iBAAiB,EAAE;YACnB,OAAO,EAAE;QACX;QAEA,WAAW;QACX,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,OAAO;gBACT,YAAY,MAAM,CAAC,SAAS;YAC9B;YAEA,IAAI,QAAQ;gBACV,YAAY,MAAM,CAAC,UAAU;YAC/B;YAEA,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CACzB,GAAG,UAAU,gBAAgB,EAAE,YAAY,QAAQ,IAAI,EACvD;gBACE,iBAAiB;YACnB;YAGF,WAAW;YACX,iBAAiB,IAAI,IAAI;YACzB,OAAO,IAAI,IAAI;QACjB,EAAE,OAAO,OAAO;YACd,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,WAAW,KAAK;gBACpE,iBAAiB,EAAE;gBACnB,WAAW;gBACX,OAAO,EAAE;YACX;YACA,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,iBAAiB,EAAE;YACnB,WAAW;YACX,OAAO,EAAE;QACX;IACF,GACA;QAAC;QAAQ;KAAU;IAGrB,MAAM,uBAAuB,OAAO;QAClC,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,gBAAgB,EAAE,IAAI;YAE/D,WAAW;YACX,OAAO,IAAI,IAAI,CAAC,OAAO;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,gCAAgC;QAC9C;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,KAAK,CAAC;YAC/C,QAAQ,IAAI,IAAI,CAAC,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,uBAAuB;QACrC;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,KAAK,CAC3B,GAAG,UAAU,cAAc,EAAE,IAAI,EACjC,CAAC,GACD;gBACE,iBAAiB;YACnB;YAEF,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBACpB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO;YAChC;YACA,OAAO,IAAI,IAAI;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,wBAAwB;YACpC,uJAAA,CAAA,UAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;YAEnC,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,MAAM,wBAAwB,CAAC,WAAW;QACxC,yBAAyB;QACzB,kBAAkB,CAAC,eACjB,aAAa,GAAG,CAAC,CAAC,UAChB,QAAQ,GAAG,KAAK,YAAY,SAAS,WAAW;QAIpD,uBAAuB;QACvB,gBAAgB,CAAC,WAAa,CAAC;gBAC7B,GAAG,QAAQ;gBACX,UACE,SAAS,QAAQ,EAAE,IAAI,CAAC,UACtB,QAAQ,GAAG,KAAK,YAAY,SAAS,WAAW,YAC7C,EAAE;YACX,CAAC;QAED,0BAA0B;QAC1B,mBAAmB,CAAC,WAAa,CAAC;gBAChC,GAAG,QAAQ;gBACX,UACE,SAAS,QAAQ,EAAE,IAAI,CAAC,UACtB,QAAQ,GAAG,KAAK,YAAY,SAAS,WAAW,YAC7C,EAAE;YACX,CAAC;QAED,wBAAwB;QACxB,iBAAiB,CAAC,WAAa,CAAC;gBAC9B,GAAG,QAAQ;gBACX,UACE,SAAS,QAAQ,EAAE,IAAI,CAAC,UACtB,QAAQ,GAAG,KAAK,YAAY,SAAS,WAAW,YAC7C,EAAE;YACX,CAAC;IACH;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,GAAG,UAAU,SAAS,EAAE,IAAI;YAC/C,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzC;IACF;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,UAAU,YAAY,CAAC;YAEtD,WAAW;YACX,eAAe,IAAI,IAAI;YAEvB,OAAO,IAAI,IAAI;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B;QAC5C;IACF,GAAG;QAAC;KAAU;IAEd,mBAAmB;IACnB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,OAAO,OAAO;QACZ,WAAW;QACX,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,OAAO;gBACT,YAAY,MAAM,CAAC,SAAS;YAC9B;YAEA,IAAI,QAAQ;gBACV,YAAY,MAAM,CAAC,UAAU;YAC/B;YAEA,MAAM,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CACzB,GAAG,UAAU,kBAAkB,EAAE,YAAY,QAAQ,IAAI;YAG3D,WAAW;YAEX,mBAAmB,IAAI,IAAI;YAE3B,OAAO,IAAI,IAAI;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,mCAAmC;QACjD;IACF,GACA;QAAC;KAAU;IAGb,MAAM,YAAY;QAChB,iBACE;QACF,iBACE;QACF,iBACE;QACF,iBACE;QACF,iBACE;QACF,iBACE;QACF,iBACE;QACF,iBACE;QACF,iBACE;QACF,kBACE;QACF,kBACE;QACF,kBACE;QACF,kBACE;QACF,kBACE;IACJ;IAEA,MAAM,oBACJ,OAAO,MAAM,CAAC,UAAU,CACtB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,CAAC,WAAW,MAAM,EAC3D;IAEH,MAAM,iBACJ,OAAO,MAAM,CAAC,UAAU,CACtB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,CAAC,WAAW,MAAM,EAC3D;IAEH,OAAO;IACP,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,mBAAmB;QAAC;KAAkB;IAC5E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,gBAAgB;QAAC;KAAe;IAEtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;QACA;QACA,qDAAqD;QACrD,IAAI,QAAQ;YACV;YACA;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/providers/UserProvider.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { UserContextProvider } from \"../context/userContext\";\nimport { SnippetsProvider } from \"../context/snippetContext\";\nimport { GlobalProvider } from \"../context/globalContext\";\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nfunction UserProvider({ children }: Props) {\n  return (\n    <UserContextProvider>\n      <GlobalProvider>\n        <SnippetsProvider>{children}</SnippetsProvider>\n      </GlobalProvider>\n    </UserContextProvider>\n  );\n}\n\nexport default UserProvider;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,SAAS,aAAa,EAAE,QAAQ,EAAS;IACvC,qBACE,8OAAC,6HAAA,CAAA,sBAAmB;kBAClB,cAAA,8OAAC,+HAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,gIAAA,CAAA,mBAAgB;0BAAE;;;;;;;;;;;;;;;;AAI3B;uCAEe", "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/public/logo.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 132, height: 189, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAA00lEQVR42gHIADf/ABoaGhd2dnZxYWFhXAoKCgoAAAAAAAAAAABwcHBq+vr6+fLy8vF/f398DAwMCwAAAAAAREREQN7e3tz+/v7/6t3r9Xhje4gNCA4QAAMDAwJGRkdFxLPO4chwuf+0Uq33WihYegABAQIDFxA4Slg4o+K/VKL/y1ac92UrU3oAFA4zQz8rod9IMbb/XDar9EolXIQNBgsPAB4VTmhFMLH3Qy6s7yMYWngDAgkKAAAAAAAGBBAVHxVPaRkSQVYDAgcJAAAAAAAAAABE9kLq+vqu6QAAAABJRU5ErkJggg==\", blurWidth: 6, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uGAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkY,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/public/Icons/SearchIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction SearchIcon({ stroke = \"black\" }: { stroke: string }) {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"20\"\n      height=\"20\"\n      viewBox=\"0 0 20 20\"\n      fill=\"none\"\n    >\n      <path\n        d=\"M14.927 15.0401L18.4001 18.4001M9.4001 5.2001C11.3883 5.2001 13.0001 6.81187 13.0001 8.8001M17.2801 9.4401C17.2801 13.77 13.77 17.2801 9.4401 17.2801C5.11018 17.2801 1.6001 13.77 1.6001 9.4401C1.6001 5.11018 5.11018 1.6001 9.4401 1.6001C13.77 1.6001 17.2801 5.11018 17.2801 9.4401Z\"\n        stroke={stroke}\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n      />\n    </svg>\n  );\n}\n\nexport default SearchIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,OAAO,EAAsB;IAC1D,qBACE,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;kBAEL,cAAA,8OAAC;YACC,GAAE;YACF,QAAQ;YACR,aAAY;YACZ,eAAc;;;;;;;;;;;AAItB;uCAEe", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/searchInput/searchInput.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useCallback, useEffect } from \"react\";\r\nimport SearchIcon from \"../../../public/Icons/SearchIcon\";\r\nimport { useSnippetContext } from \"@/context/snippetContext\";\r\nimport lodash from \"lodash\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useUserContext } from \"@/context/userContext\";\r\n\r\ninterface Props {\r\n  wFull?: boolean;\r\n}\r\nconst SearchInput = ({ wFull }: Props) => {\r\n  const {\r\n    getPublicSnippets,\r\n    getPopularSnippets,\r\n    getLikedSnippets,\r\n    getUserSnippets,\r\n    getLeaderboard,\r\n  } = useSnippetContext();\r\n\r\n  const [searchQuery, setSearchQuery] = React.useState(\"\");\r\n\r\n  const userId = useUserContext().user?._id;\r\n\r\n  const pathname = usePathname();\r\n\r\n  const debouncedSearchQuery = useCallback(\r\n    lodash.debounce(function (query: string) {\r\n      if (query) {\r\n        switch (pathname) {\r\n          case \"/\":\r\n            getPublicSnippets(\"\", \"\", query);\r\n            break;\r\n          case \"/popular\":\r\n            getPopularSnippets(\"\", query);\r\n            break;\r\n          case \"/favourites\":\r\n            getLikedSnippets(\"\", query);\r\n            break;\r\n          case \"/mysnippets\":\r\n            getUserSnippets(\"\", query);\r\n            break;\r\n        }\r\n      } else {\r\n        getPublicSnippets();\r\n        getPopularSnippets();\r\n        getLeaderboard();\r\n\r\n        if (userId) {\r\n          getLikedSnippets();\r\n          getUserSnippets();\r\n        }\r\n      }\r\n    }, 500),\r\n    [\r\n      pathname,\r\n      getPublicSnippets,\r\n      getPopularSnippets,\r\n      getLeaderboard,\r\n      getLikedSnippets,\r\n      getUserSnippets,\r\n      userId,\r\n    ]\r\n  );\r\n\r\n  useEffect(() => {\r\n    debouncedSearchQuery(searchQuery);\r\n\r\n    // cnacel the debounce function on unmount\r\n    return () => {\r\n      debouncedSearchQuery.cancel();\r\n    };\r\n  }, [searchQuery, debouncedSearchQuery]);\r\n\r\n  return (\r\n    <form\r\n      className={`relative flex gap-4 overflow-hidden ${\r\n        wFull ? \"w-full\" : \"md:w-[580px]\"\r\n      }`}\r\n    >\r\n      <div className=\"absolute top-[50%] left-3 translate-y-[-50%]\">\r\n        <SearchIcon stroke=\"rgba(249,249,249,0.6)\" />\r\n      </div>\r\n      <input\r\n        type=\"text\"\r\n        value={searchQuery}\r\n        onChange={(e) => setSearchQuery(e.target.value)}\r\n        className={`pl-10 pr-2 py-2 bg-[#212121] text-gray-200 font-medium border-[2px] border-[#ffffff1a] rounded-lg outline-none transition-all duration-300 ease-in-out focus:w-full focus:border-[#ffffff4d] ${\r\n          wFull ? \"w-full\" : \"w-[90%]\"\r\n        }`}\r\n      />\r\n      {!searchQuery && (\r\n        <span className=\"absolute top-[50%] left-10 translate-y-[-50%] text-gray-400 pointer-events-none text-nowrap\">\r\n          Search for snippets e.g. Nested Loops etc.\r\n        </span>\r\n      )}\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default SearchInput;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAWA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAS;IACnC,MAAM,EACJ,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,cAAc,EACf,GAAG,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAErD,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,IAAI,IAAI,EAAE;IAEtC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,gIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,SAAU,KAAa;QACrC,IAAI,OAAO;YACT,OAAQ;gBACN,KAAK;oBACH,kBAAkB,IAAI,IAAI;oBAC1B;gBACF,KAAK;oBACH,mBAAmB,IAAI;oBACvB;gBACF,KAAK;oBACH,iBAAiB,IAAI;oBACrB;gBACF,KAAK;oBACH,gBAAgB,IAAI;oBACpB;YACJ;QACF,OAAO;YACL;YACA;YACA;YAEA,IAAI,QAAQ;gBACV;gBACA;YACF;QACF;IACF,GAAG,MACH;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;QAErB,0CAA0C;QAC1C,OAAO;YACL,qBAAqB,MAAM;QAC7B;IACF,GAAG;QAAC;QAAa;KAAqB;IAEtC,qBACE,8OAAC;QACC,WAAW,CAAC,oCAAoC,EAC9C,QAAQ,WAAW,gBACnB;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8HAAA,CAAA,UAAU;oBAAC,QAAO;;;;;;;;;;;0BAErB,8OAAC;gBACC,MAAK;gBACL,OAAO;gBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gBAC9C,WAAW,CAAC,6LAA6L,EACvM,QAAQ,WAAW,WACnB;;;;;;YAEH,CAAC,6BACA,8OAAC;gBAAK,WAAU;0BAA8F;;;;;;;;;;;;AAMtH;uCAEe", "debugId": null}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/utils/Icons.tsx"], "sourcesContent": ["export const bookmarkEmpty = <i className=\"fa-regular fa-bookmark\" />;\nexport const bookmarkFilled = <i className=\"fa-solid fa-bookmark\" />;\nexport const copy = <i className=\"fa-regular fa-copy\" />;\nexport const heart = <i className=\"fa-solid fa-heart\" />;\nexport const heartOutline = <i className=\"fa-regular fa-heart\" />;\nexport const home = <i className=\"fa-solid fa-house\" />;\nexport const bookmarkIcon = <i className=\"fa-solid fa-bookmark\" />;\nexport const box = <i className=\"fa-solid fa-box\" />;\nexport const help = <i className=\"fa-solid fa-circle-info\" />;\nexport const gear = <i className=\"fa-solid fa-gear\" />;\nexport const users = <i className=\"fa-solid fa-user-group\" />;\nexport const fire = <i className=\"fa-solid fa-fire-flame-curved\" />;\nexport const signout = <i className=\"fa-solid fa-right-from-bracket\" />;\nexport const github = <i className=\"fa-brands fa-github\" />;\nexport const linkedin = <i className=\"fa-brands fa-linkedin\" />;\nexport const envelope = <i className=\"fa-solid fa-envelope\" />;\nexport const arrowLeft = <i className=\"fa-solid fa-arrow-left fa-beat-fade\" />;\nexport const bars = <i className=\"fa-solid fa-bars fa-beat\" />;\nexport const trash = <i className=\"fa-solid fa-trash\" />;\nexport const edit = <i className=\"fa-solid fa-pen-to-square\" />;\nexport const login = <i className=\"fa-solid fa-right-to-bracket\" />;\nexport const register = <i className=\"fa-solid fa-user-plus\" />;\nexport const down = <i className=\"fa-solid fa-chevron-down\" />;\nexport const up = <i className=\"fa-solid fa-chevron-up\" />;\nexport const prev = <i className=\"fa-solid fa-backward\" />;\nexport const next = <i className=\"fa-solid fa-forward\" />;\nexport const plus = <i className=\"fa-solid fa-plus\" />;\nexport const cross = <i className=\"fa-solid fa-x\" />;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,8BAAgB,8OAAC;IAAE,WAAU;;;;;;AACnC,MAAM,+BAAiB,8OAAC;IAAE,WAAU;;;;;;AACpC,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,sBAAQ,8OAAC;IAAE,WAAU;;;;;;AAC3B,MAAM,6BAAe,8OAAC;IAAE,WAAU;;;;;;AAClC,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,6BAAe,8OAAC;IAAE,WAAU;;;;;;AAClC,MAAM,oBAAM,8OAAC;IAAE,WAAU;;;;;;AACzB,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,sBAAQ,8OAAC;IAAE,WAAU;;;;;;AAC3B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,wBAAU,8OAAC;IAAE,WAAU;;;;;;AAC7B,MAAM,uBAAS,8OAAC;IAAE,WAAU;;;;;;AAC5B,MAAM,yBAAW,8OAAC;IAAE,WAAU;;;;;;AAC9B,MAAM,yBAAW,8OAAC;IAAE,WAAU;;;;;;AAC9B,MAAM,0BAAY,8OAAC;IAAE,WAAU;;;;;;AAC/B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,sBAAQ,8OAAC;IAAE,WAAU;;;;;;AAC3B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,sBAAQ,8OAAC;IAAE,WAAU;;;;;;AAC3B,MAAM,yBAAW,8OAAC;IAAE,WAAU;;;;;;AAC9B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,mBAAK,8OAAC;IAAE,WAAU;;;;;;AACxB,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,qBAAO,8OAAC;IAAE,WAAU;;;;;;AAC1B,MAAM,sBAAQ,8OAAC;IAAE,WAAU", "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/header/header.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useUserContext } from \"@/context/userContext\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport React from \"react\";\r\nimport logo from \"@/../public/logo.png\";\r\nimport SearchInput from \"../searchInput/searchInput\";\r\nimport { login, register } from \"@/utils/Icons\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nimport SearchIcon from \"../../../public/Icons/SearchIcon\";\r\n\r\nconst Header = () => {\r\n  const { user } = useUserContext();\r\n  const {\r\n    openModalForSnippet,\r\n    openProfileModal,\r\n    openModalForSearch,\r\n    openEmailVerificationModal,\r\n  } = useGlobalContext();\r\n\r\n  const photo = user?.photo;\r\n  const router = useRouter();\r\n  return (\r\n    <div className=\"fixed z-20 top-0 w-full px-8 flex items-center justify-between bg-[#252525] border-b-[2px] border-[#ffffff1a] h-[10vh]\">\r\n      <Link href={\"/\"} className=\"flex items-center gap-2\">\r\n        <Image\r\n          src={logo}\r\n          alt=\"Logo\"\r\n          width={25}\r\n          height={25}\r\n          className=\"ml=[1px]\"\r\n        />\r\n        <h1 className=\"flex items-center text-white text-2xl font-bold\">\r\n          Snippy\r\n        </h1>\r\n      </Link>\r\n\r\n      <div className=\"lg:flex hidden\">\r\n        <SearchInput />\r\n      </div>\r\n\r\n      {!user._id ? (\r\n        <div className=\"flex items-center gap-4\">\r\n          <button\r\n            className=\"btn-hover relative h-[47px] px-8 bg-[#3A3B3C] flex items-center justify-center gap-4 rounded-xl overflow-hidden hover:cursor-pointer\"\r\n            onClick={() => router.push(\"/login\")}\r\n          >\r\n            <span className=\"text-xl text-gray-200\">{login}</span>\r\n            <span className=\"font-bold text-white\">Login</span>\r\n            <div className=\"blob\"></div>\r\n          </button>\r\n          <button\r\n            className=\"btn-hover relative h-[47px] px-8 bg-[#7263F3] flex items-center justify-center gap-4 rounded-xl overflow-hidden hover:cursor-pointer\"\r\n            onClick={() => router.push(\"/register\")}\r\n          >\r\n            <span className=\"text-xl text-gray-200\">{register}</span>\r\n            <span className=\"font-bold text-white\">Register</span>\r\n            <div className=\"blob\"></div>\r\n          </button>\r\n        </div>\r\n      ) : (\r\n        <div className=\"flex items-center gap-2\">\r\n          <button\r\n            className=\"mr-4 h-[42px] px-4 flex items-center justify-center bg-white rounded-lg font-semibold hover:bg-white/80 transition duration-200 ease-in-out\"\r\n            onClick={() => {\r\n              if (user?.isVerified) {\r\n                openModalForSnippet();\r\n              } else {\r\n                openEmailVerificationModal();\r\n              }\r\n            }}\r\n          >\r\n            Create Snippet\r\n          </button>\r\n\r\n          <button\r\n            onClick={openModalForSearch}\r\n            className=\"w-[45px] h-[45px] flex items-center justify-center bg-[#ffffff0d] rounded-lg lg:hidden\"\r\n          >\r\n            <SearchIcon stroke=\"rgba(249,249,249,0.6) \"></SearchIcon>\r\n          </button>\r\n\r\n          <button\r\n            onClick={openProfileModal}\r\n            className=\"w-[43px] h-[42px] flex justify-center items-center bg-[#ffffff0d] rounded-lg\"\r\n          >\r\n            <Image\r\n              src={photo || \"/images/User.png\"}\r\n              alt=\"profile\"\r\n              width={35}\r\n              height={35}\r\n              className=\"rounded-lg\"\r\n            />\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,MAAM,SAAS;IACb,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,EACJ,mBAAmB,EACnB,gBAAgB,EAChB,kBAAkB,EAClB,0BAA0B,EAC3B,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,QAAQ,MAAM;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM;gBAAK,WAAU;;kCACzB,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,4PAAA,CAAA,UAAI;wBACT,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,8OAAC;wBAAG,WAAU;kCAAkD;;;;;;;;;;;;0BAKlE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gJAAA,CAAA,UAAW;;;;;;;;;;YAGb,CAAC,KAAK,GAAG,iBACR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,OAAO,IAAI,CAAC;;0CAE3B,8OAAC;gCAAK,WAAU;0CAAyB,sHAAA,CAAA,QAAK;;;;;;0CAC9C,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,OAAO,IAAI,CAAC;;0CAE3B,8OAAC;gCAAK,WAAU;0CAAyB,sHAAA,CAAA,WAAQ;;;;;;0CACjD,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;qCAInB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS;4BACP,IAAI,MAAM,YAAY;gCACpB;4BACF,OAAO;gCACL;4BACF;wBACF;kCACD;;;;;;kCAID,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,8HAAA,CAAA,UAAU;4BAAC,QAAO;;;;;;;;;;;kCAGrB,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,SAAS;4BACd,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe", "debugId": null}}, {"offset": {"line": 1593, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/sidebar/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { useUserContext } from \"@/context/userContext\";\r\nimport {\r\n  arrowLeft,\r\n  bars,\r\n  box,\r\n  fire,\r\n  gear,\r\n  heart,\r\n  help,\r\n  home,\r\n  users,\r\n} from \"@/utils/Icons\";\r\nimport Link from \"next/link\";\r\n\r\nimport { useRouter } from \"nextjs-toploader/app\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nconst Sidebar = () => {\r\n  const { user } = useUserContext();\r\n  const { isSidebarOpen, setIsSidebarOpen } = useGlobalContext();\r\n\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const menu = [\r\n    {\r\n      id: 1,\r\n      name: isSidebarOpen ? \"Home\" : \"\",\r\n      url: \"/\",\r\n      icon: home,\r\n    },\r\n\r\n    {\r\n      id: 4,\r\n      name: isSidebarOpen ? \"Popular\" : \"\",\r\n      url: \"/popular\",\r\n      icon: fire,\r\n    },\r\n    {\r\n      id: 5,\r\n      name: isSidebarO<PERSON> ? \"Top Creators\" : \"\",\r\n      url: `${user ? \"/leaderboard\" : \"/login\"}`,\r\n      icon: users,\r\n    },\r\n    {\r\n      id: 2,\r\n      name: isSidebarOpen ? \"Favourites\" : \"\",\r\n      url: `${user ? \"/favourites\" : \"/login\"}`,\r\n      icon: heart,\r\n    },\r\n    {\r\n      id: 3,\r\n      name: isSidebarOpen ? \"My Snippets\" : \"\",\r\n      url: `${user ? \"/mysnippets\" : \"/login\"}`,\r\n      icon: box,\r\n    },\r\n    {\r\n      id: 1,\r\n      name: isSidebarOpen ? \"Profile\" : \"\",\r\n      url: `${user._id ? \"/profile\" : \"/login\"}`,\r\n      icon: gear,\r\n    },\r\n    {\r\n      id: 2,\r\n      name: isSidebarOpen ? \"Help\" : \"\",\r\n      url: \"/helpcenter\",\r\n      icon: help,\r\n    },\r\n  ];\r\n\r\n  const getIconColor = (url: string) => {\r\n    return pathname === url ? \"#aaa\" : \"#71717a\";\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`mt-4 fixed z-20 bg-[#212121] h-full border-r-[2px] border-[#ffffff0d] ${\r\n        isSidebarOpen ? \"w-[15rem]\" : \"w-[5.2rem]\"\r\n      }`}\r\n    >\r\n      <span\r\n        className=\"u-shadow-2 w-[45px] absolute z-50 top-[21px] right-[-47px] py-[0.8rem] bg-[#252525] cursor-pointer\r\n        text-xl text-gray-400 flex items-center justify-center rounded-tr-lg rounded-br-lg\"\r\n        onClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n      >\r\n        {isSidebarOpen ? arrowLeft : bars}\r\n      </span>\r\n      <nav className=\"h-full flex flex-col justify-between\">\r\n        <div className=\"mt-4 flex-1 flex flex-col justify-between\">\r\n          <ul>\r\n            {menu.slice(0, -2).map((item) => {\r\n              return (\r\n                <li\r\n                  key={item.id}\r\n                  className={`sidebar-nav-item my-[.3rem] px-8 py-[.6rem] cursor-pointer ${\r\n                    pathname === item.url && \"active-nav-item\"\r\n                  }`}\r\n                  onClick={() => {\r\n                    router.push(item.url);\r\n                  }}\r\n                >\r\n                  <Link\r\n                    href={item.url}\r\n                    className=\"grid grid-cols-[40px_1fr] items-center text-gray-200\"\r\n                  >\r\n                    <span\r\n                      className=\"text-lg\"\r\n                      style={{ color: getIconColor(item.url) }}\r\n                    >\r\n                      {item.icon}\r\n                    </span>\r\n                    <span>{item.name}</span>\r\n                  </Link>\r\n                </li>\r\n              );\r\n            })}\r\n          </ul>\r\n\r\n          <ul className={`${isSidebarOpen ? \"mb-2\" : \"mb-[5.5rem]\"}`}>\r\n            {menu.slice(-2).map((item) => {\r\n              return (\r\n                <li\r\n                  key={item.id}\r\n                  className={`sidebar-nav-item my-[.3rem] px-8 py-[.6rem] cursor-pointer ${\r\n                    pathname === item.url && \"active-nav-item\"\r\n                  }`}\r\n                  onClick={() => {\r\n                    router.push(item.url);\r\n                  }}\r\n                >\r\n                  <Link\r\n                    href={item.url}\r\n                    className=\"grid grid-cols-[40px_1fr] items-center text-gray-200\"\r\n                  >\r\n                    <span\r\n                      className=\"text-lg\"\r\n                      style={{ color: getIconColor(item.url) }}\r\n                    >\r\n                      {item.icon}\r\n                    </span>\r\n                    <span>{item.name}</span>\r\n                  </Link>\r\n                </li>\r\n              );\r\n            })}\r\n          </ul>\r\n        </div>\r\n\r\n        {isSidebarOpen && (\r\n          <footer className=\"mb-[5rem] p-4 border-t-[2px] border-rgba-3 text-gray-300\">\r\n            <p className=\"text-center text-sm mt-4\">\r\n              &copy; {new Date().getFullYear()}{\" \"}\r\n              <Link href={\"/\"}>Kapil Agrawal</Link>. All&nbsp;rights reserved.\r\n            </p>\r\n          </footer>\r\n        )}\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAWA;AAEA;AACA;AACA;AAlBA;;;;;;;;AAmBA,MAAM,UAAU;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAE3D,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM,gBAAgB,SAAS;YAC/B,KAAK;YACL,MAAM,sHAAA,CAAA,OAAI;QACZ;QAEA;YACE,IAAI;YACJ,MAAM,gBAAgB,YAAY;YAClC,KAAK;YACL,MAAM,sHAAA,CAAA,OAAI;QACZ;QACA;YACE,IAAI;YACJ,MAAM,gBAAgB,iBAAiB;YACvC,KAAK,GAAG,OAAO,iBAAiB,UAAU;YAC1C,MAAM,sHAAA,CAAA,QAAK;QACb;QACA;YACE,IAAI;YACJ,MAAM,gBAAgB,eAAe;YACrC,KAAK,GAAG,OAAO,gBAAgB,UAAU;YACzC,MAAM,sHAAA,CAAA,QAAK;QACb;QACA;YACE,IAAI;YACJ,MAAM,gBAAgB,gBAAgB;YACtC,KAAK,GAAG,OAAO,gBAAgB,UAAU;YACzC,MAAM,sHAAA,CAAA,MAAG;QACX;QACA;YACE,IAAI;YACJ,MAAM,gBAAgB,YAAY;YAClC,KAAK,GAAG,KAAK,GAAG,GAAG,aAAa,UAAU;YAC1C,MAAM,sHAAA,CAAA,OAAI;QACZ;QACA;YACE,IAAI;YACJ,MAAM,gBAAgB,SAAS;YAC/B,KAAK;YACL,MAAM,sHAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,eAAe,CAAC;QACpB,OAAO,aAAa,MAAM,SAAS;IACrC;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,sEAAsE,EAChF,gBAAgB,cAAc,cAC9B;;0BAEF,8OAAC;gBACC,WAAU;gBAEV,SAAS,IAAM,iBAAiB,CAAC;0BAEhC,gBAAgB,sHAAA,CAAA,YAAS,GAAG,sHAAA,CAAA,OAAI;;;;;;0BAEnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CACE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;oCACtB,qBACE,8OAAC;wCAEC,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,GAAG,IAAI,mBACzB;wCACF,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,GAAG;wCACtB;kDAEA,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,GAAG;4CACd,WAAU;;8DAEV,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,aAAa,KAAK,GAAG;oDAAE;8DAEtC,KAAK,IAAI;;;;;;8DAEZ,8OAAC;8DAAM,KAAK,IAAI;;;;;;;;;;;;uCAlBb,KAAK,EAAE;;;;;gCAsBlB;;;;;;0CAGF,8OAAC;gCAAG,WAAW,GAAG,gBAAgB,SAAS,eAAe;0CACvD,KAAK,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;oCACnB,qBACE,8OAAC;wCAEC,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,GAAG,IAAI,mBACzB;wCACF,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,GAAG;wCACtB;kDAEA,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,GAAG;4CACd,WAAU;;8DAEV,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,aAAa,KAAK,GAAG;oDAAE;8DAEtC,KAAK,IAAI;;;;;;8DAEZ,8OAAC;8DAAM,KAAK,IAAI;;;;;;;;;;;;uCAlBb,KAAK,EAAE;;;;;gCAsBlB;;;;;;;;;;;;oBAIH,+BACC,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAE,WAAU;;gCAA2B;gCAC9B,IAAI,OAAO,WAAW;gCAAI;8CAClC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;8CAAK;;;;;;gCAAoB;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;uCAEe", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/providers/ContentProvider.tsx"], "sourcesContent": ["\"use client\";\r\nimport Sidebar from \"@/components/sidebar/sidebar\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport React from \"react\";\r\n\r\ninterface Props {\r\n  children: React.ReactNode;\r\n}\r\n\r\nfunction ContentProvider({ children }: Props) {\r\n  const { isSidebarOpen } = useGlobalContext();\r\n\r\n  const pathname = usePathname();\r\n\r\n  // Hide sidebar on these paths\r\n  const hideSidebarPaths = [\r\n    \"/login\",\r\n    \"/register\",\r\n    \"/forgot-password\",\r\n    \"/reset-password\",\r\n    \"/verify-email\",\r\n  ];\r\n\r\n  // Check for dynamic reset-password route\r\n  const isResetPasswordPage = pathname.startsWith(\"/reset-password\");\r\n  const isUserVerifyPage = pathname.startsWith(\"/verify-email\");\r\n\r\n  const marginClass =\r\n    hideSidebarPaths.includes(pathname) ||\r\n    isResetPasswordPage ||\r\n    isUserVerifyPage\r\n      ? \"ml-0\"\r\n      : isSidebarOpen\r\n      ? \"ml-[15rem]\"\r\n      : \"ml-[5.2rem]\";\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {!(\r\n        hideSidebarPaths.includes(pathname) ||\r\n        isResetPasswordPage ||\r\n        isUserVerifyPage\r\n      ) && <Sidebar />}\r\n      <div className={`mt-[8vh] ${marginClass}`}>{children}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ContentProvider;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAUA,SAAS,gBAAgB,EAAE,QAAQ,EAAS;IAC1C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAEzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IAED,yCAAyC;IACzC,MAAM,sBAAsB,SAAS,UAAU,CAAC;IAChD,MAAM,mBAAmB,SAAS,UAAU,CAAC;IAE7C,MAAM,cACJ,iBAAiB,QAAQ,CAAC,aAC1B,uBACA,mBACI,SACA,gBACA,eACA;IAEN,qBACE,8OAAC;QAAI,WAAU;;YACZ,CAAC,CACA,iBAAiB,QAAQ,CAAC,aAC1B,uBACA,gBACF,mBAAK,8OAAC,wIAAA,CAAA,UAAO;;;;;0BACb,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,aAAa;0BAAG;;;;;;;;;;;;AAGlD;uCAEe", "debugId": null}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/button/button.tsx"], "sourcesContent": ["import clsx from \"clsx\";\r\nimport React from \"react\";\r\n\r\ninterface Props {\r\n  className?: string;\r\n  onClick?: () => void;\r\n  children: React.ReactNode;\r\n  style?: React.CSSProperties;\r\n  type?: \"button\" | \"submit\" | \"reset\";\r\n}\r\n\r\nfunction Button({ className, onClick, children, style, type }: Props) {\r\n  const defaultClasses =\r\n    \"h-[42px] px-4 flex items-center justify-center bg-white rounded-lg font-semibold hover:bg-white/80 transition duration-200 ease-in-out\";\r\n  return (\r\n    <button\r\n      className={clsx(defaultClasses, className)}\r\n      onClick={onClick}\r\n      style={style}\r\n      type={type || \"button\"}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n\r\nexport default Button;"], "names": [], "mappings": ";;;;AAAA;;;AAWA,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAS;IAClE,MAAM,iBACJ;IACF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,gBAAgB;QAChC,SAAS;QACT,OAAO;QACP,MAAM,QAAQ;kBAEb;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/modals/addSnippetModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nimport { useSnippetContext } from \"@/context/snippetContext\";\r\nimport { ITag } from \"@/types/types\";\r\nimport { edit, plus } from \"@/utils/Icons\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Button from \"../button/button\";\r\n\r\nfunction AddSnippetModal() {\r\n  const { modalMode, closeModal, activeSnippet } = useGlobalContext();\r\n  const { createSnippet, tags, useTagColorMemo, updateSnippet } =\r\n    useSnippetContext();\r\n\r\n  const [activeTags, setActiveTags] = useState<ITag[]>([]);\r\n\r\n  const [title, setTitle] = useState(\"\");\r\n  const [description, setDescription] = useState(\"\");\r\n  const [code, setCode] = useState(\"\");\r\n  const [language, setLanguage] = useState(\"javascript\");\r\n  const [isPublic, setIsPublic] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (modalMode === \"edit-snippet\" && activeSnippet) {\r\n      // initialize activeTags and form fields with the activeSnippet data\r\n      setActiveTags(activeSnippet.tags);\r\n      setTitle(activeSnippet.title);\r\n      setDescription(activeSnippet.description);\r\n      setCode(activeSnippet.code);\r\n      setLanguage(activeSnippet.language);\r\n      setIsPublic(activeSnippet.isPublic);\r\n    }\r\n  }, [modalMode, activeSnippet]);\r\n\r\n  const resetForm = () => {\r\n    setTitle(\"\");\r\n    setDescription(\"\");\r\n    setCode(\"\");\r\n    setLanguage(\"javascript\");\r\n    setIsPublic(true);\r\n    setActiveTags([]);\r\n  };\r\n\r\n  const languages = [\r\n    \"c\",\r\n    \"c#\",\r\n    \"c++\",\r\n    \"css\",\r\n    \"django\",\r\n    \"go\",\r\n    \"haskell\",\r\n    \"html\",\r\n    \"java\",\r\n    \"javascript\",\r\n    \"json\",\r\n    \"kotlin\",\r\n    \"lua\",\r\n    \"php\",\r\n    \"python\",\r\n    \"r\",\r\n    \"ruby\",\r\n    \"rust\",\r\n    \"sql\",\r\n    \"swift\",\r\n    \"typescript\",\r\n  ];\r\n\r\n  const handleTags = (tag: ITag) => {\r\n    const isTagActive = activeTags.some((activeTag: { _id: string }) => {\r\n      return activeTag._id === tag._id;\r\n    });\r\n\r\n    if (isTagActive) {\r\n      // remove from active tags\r\n      setActiveTags(\r\n        activeTags.filter((activeTag: { _id: string }) => {\r\n          return activeTag._id !== tag._id;\r\n        })\r\n      );\r\n    } else {\r\n      // add to active tags\r\n      setActiveTags([...activeTags, tag]);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault();\r\n\r\n    const snippetData = {\r\n      _id: activeSnippet?._id,\r\n      title,\r\n      description,\r\n      code,\r\n      language,\r\n      isPublic,\r\n      tags: activeTags.length > 0 ? activeTags.map((tag: ITag) => tag._id) : [],\r\n    };\r\n\r\n    if (modalMode === \"edit-snippet\") {\r\n      updateSnippet(snippetData);\r\n\r\n      closeModal();\r\n    } else if (modalMode === \"add-snippet\") {\r\n      const res = createSnippet(snippetData);\r\n\r\n      if (res._id) {\r\n        closeModal();\r\n        resetForm();\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed top-0 left-0 z-40 h-full w-full bg-[#000]/30 backdrop-blur-sm bg-opacity-50 overflow-hidden\">\r\n      <div className=\"py-5 px-6 bg-[#181818] max-w-[920px] w-full flex flex-col gap-4 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-lg shadow-md\">\r\n        <form action=\"\" className=\"flex flex-col gap-4\" onSubmit={handleSubmit}>\r\n          <h1 className=\"text-white text-3xl font-bold\">\r\n            {modalMode === \"edit-snippet\" ? (\r\n              <span className=\"flex items-center gap-4\">\r\n                {edit} Edit Snippet\r\n              </span>\r\n            ) : (\r\n              <span className=\"flex items-center gap-4\">{plus}Add Snippet</span>\r\n            )}\r\n          </h1>\r\n\r\n          <div className=\"flex justify-between gap-4\">\r\n            <div className=\"flex-1\">\r\n              <input\r\n                type=\"text\"\r\n                name=\"title\"\r\n                value={title}\r\n                onChange={(e) => setTitle(e.target.value)}\r\n                placeholder=\"Title\"\r\n                className=\"w-full h-12 px-4 bg-[#252525] text-white rounded-lg\"\r\n              />\r\n            </div>\r\n            <div>\r\n              <select\r\n                name=\"language\"\r\n                value={language}\r\n                onChange={(e) => setLanguage(e.target.value)}\r\n                className=\"w-full h-12 px-4 bg-[#252525] text-white rounded-lg cursor-pointer\"\r\n              >\r\n                {languages.map((lang) => {\r\n                  return (\r\n                    <option key={lang} value={lang}>\r\n                      {lang}\r\n                    </option>\r\n                  );\r\n                })}\r\n              </select>\r\n            </div>\r\n\r\n            <div>\r\n              <select\r\n                name=\"isPublic\"\r\n                value={isPublic.toString()}\r\n                onChange={(e) => setIsPublic(e.target.value === \"true\")}\r\n                className=\"w-full h-12 px-4 bg-[#252525] text-white rounded-lg cursor-pointer\"\r\n              >\r\n                <option value=\"true\">Public</option>\r\n                <option value=\"false\">Private</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <div>\r\n            <textarea\r\n              name=\"description\"\r\n              value={description}\r\n              onChange={(e) => setDescription(e.target.value)}\r\n              placeholder=\"Description\"\r\n              className=\"w-full pt-2 px-4 bg-[#252525] text-white rounded-lg\"\r\n              rows={2}\r\n            ></textarea>\r\n          </div>\r\n\r\n          <div>\r\n            <pre>\r\n              <code>\r\n                <textarea\r\n                  name=\"code\"\r\n                  value={code}\r\n                  onChange={(e) => setCode(e.target.value)}\r\n                  className=\"w-full pt-2 h-[400px] px-4 bg-[#252525] text-white rounded-lg\"\r\n                  placeholder=\"// Code here...\"\r\n                ></textarea>\r\n              </code>\r\n            </pre>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-4\">\r\n            {tags.map((tag: ITag) => {\r\n              return (\r\n                <Button\r\n                  key={tag._id}\r\n                  type=\"button\"\r\n                  className=\"py-1 text-white text-sm\"\r\n                  style={{\r\n                    background: activeTags.some((activeTag: ITag) => {\r\n                      return activeTag._id === tag._id;\r\n                    })\r\n                      ? \"#7263f3\"\r\n                      : useTagColorMemo,\r\n                  }}\r\n                  onClick={() => handleTags(tag)}\r\n                >\r\n                  {tag.name}\r\n                </Button>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          <div className=\"flex justify-end gap-4\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"h-[47px] flex items-center justify-center px-8 bg-red-500 text-white hover:bg-red-500/80 rounded-md transition-all duration-300 ease-in-out\"\r\n              style={{ fontWeight: 400 }}\r\n              onClick={closeModal}\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              className={`h-[47px] flex items-center justify-center py-1 px-8 ${\r\n                modalMode === \"edit\" ? \"bg-blue-400\" : \"bg-indigo-400\"\r\n              } text-white hover:bg-indigo-400/90 rounded-md transition-all duration-300 ease-in-out`}\r\n              type=\"submit\"\r\n              style={{ fontWeight: 400 }}\r\n            >\r\n              {modalMode === \"edit-snippet\" ? \"Update Snippet\" : \"Add Snippet\"}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddSnippetModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAChE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,eAAe,EAAE,aAAa,EAAE,GAC3D,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAElB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAEvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,kBAAkB,eAAe;YACjD,oEAAoE;YACpE,cAAc,cAAc,IAAI;YAChC,SAAS,cAAc,KAAK;YAC5B,eAAe,cAAc,WAAW;YACxC,QAAQ,cAAc,IAAI;YAC1B,YAAY,cAAc,QAAQ;YAClC,YAAY,cAAc,QAAQ;QACpC;IACF,GAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,YAAY;QAChB,SAAS;QACT,eAAe;QACf,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,cAAc,EAAE;IAClB;IAEA,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,WAAW,IAAI,CAAC,CAAC;YACnC,OAAO,UAAU,GAAG,KAAK,IAAI,GAAG;QAClC;QAEA,IAAI,aAAa;YACf,0BAA0B;YAC1B,cACE,WAAW,MAAM,CAAC,CAAC;gBACjB,OAAO,UAAU,GAAG,KAAK,IAAI,GAAG;YAClC;QAEJ,OAAO;YACL,qBAAqB;YACrB,cAAc;mBAAI;gBAAY;aAAI;QACpC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,MAAM,cAAc;YAClB,KAAK,eAAe;YACpB;YACA;YACA;YACA;YACA;YACA,MAAM,WAAW,MAAM,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC,MAAc,IAAI,GAAG,IAAI,EAAE;QAC3E;QAEA,IAAI,cAAc,gBAAgB;YAChC,cAAc;YAEd;QACF,OAAO,IAAI,cAAc,eAAe;YACtC,MAAM,MAAM,cAAc;YAE1B,IAAI,IAAI,GAAG,EAAE;gBACX;gBACA;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,QAAO;gBAAG,WAAU;gBAAsB,UAAU;;kCACxD,8OAAC;wBAAG,WAAU;kCACX,cAAc,+BACb,8OAAC;4BAAK,WAAU;;gCACb,sHAAA,CAAA,OAAI;gCAAC;;;;;;iDAGR,8OAAC;4BAAK,WAAU;;gCAA2B,sHAAA,CAAA,OAAI;gCAAC;;;;;;;;;;;;kCAIpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;;;;;;;;;;;0CAGd,8OAAC;0CACC,cAAA,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;8CAET,UAAU,GAAG,CAAC,CAAC;wCACd,qBACE,8OAAC;4CAAkB,OAAO;sDACvB;2CADU;;;;;oCAIjB;;;;;;;;;;;0CAIJ,8OAAC;0CACC,cAAA,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK,KAAK;oCAChD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;kCACC,cAAA,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,aAAY;4BACZ,WAAU;4BACV,MAAM;;;;;;;;;;;kCAIV,8OAAC;kCACC,cAAA,8OAAC;sCACC,cAAA,8OAAC;0CACC,cAAA,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,WAAU;oCACV,aAAY;;;;;;;;;;;;;;;;;;;;;kCAKpB,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,qBACE,8OAAC,sIAAA,CAAA,UAAM;gCAEL,MAAK;gCACL,WAAU;gCACV,OAAO;oCACL,YAAY,WAAW,IAAI,CAAC,CAAC;wCAC3B,OAAO,UAAU,GAAG,KAAK,IAAI,GAAG;oCAClC,KACI,YACA;gCACN;gCACA,SAAS,IAAM,WAAW;0CAEzB,IAAI,IAAI;+BAZJ,IAAI,GAAG;;;;;wBAelB;;;;;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,YAAY;gCAAI;gCACzB,SAAS;0CACV;;;;;;0CAGD,8OAAC;gCACC,WAAW,CAAC,oDAAoD,EAC9D,cAAc,SAAS,gBAAgB,gBACxC,qFAAqF,CAAC;gCACvF,MAAK;gCACL,OAAO;oCAAE,YAAY;gCAAI;0CAExB,cAAc,iBAAiB,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjE;uCAEe", "debugId": null}}, {"offset": {"line": 2276, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/modals/profileModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nimport { useSnippetContext } from \"@/context/snippetContext\";\r\nimport { useUserContext } from \"@/context/userContext\";\r\nimport { cross, gear, signout } from \"@/utils/Icons\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport React from \"react\";\r\n\r\nconst ProfileModal = () => {\r\n  const { logoutUser } = useUserContext();\r\n  const { getPublicSnippets, getPopularSnippets, getLeaderboard } =\r\n    useSnippetContext();\r\n  const { closeModal } = useGlobalContext();\r\n  const router = useRouter();\r\n\r\n  const menu = [\r\n    {\r\n      name: \"Settings\",\r\n      url: \"/profile\",\r\n      icon: gear,\r\n      onClick: () => {\r\n        closeModal();\r\n        router.push(\"/profile\");\r\n      },\r\n    },\r\n    {\r\n      name: \"Sign Out\",\r\n      url: \"/\",\r\n      icon: signout,\r\n      onClick: () => {\r\n        closeModal();\r\n        getPublicSnippets();\r\n        getPopularSnippets();\r\n        getLeaderboard();\r\n        logoutUser();\r\n        router.push(\"/\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"u-shadow-1 fixed z-30 right-8 top-[4.2rem] bg-[#252525] rounded-lg border border-[#ffffff1a] flex justify-center\">\r\n      <nav>\r\n        <ul className=\"py-1 min-w-[230px]\">\r\n          {menu.map((item, index) => (\r\n            <li\r\n              key={index}\r\n              className=\"sidebar-nav-item my-[.3rem] px-8 py-[.6rem] cursor-pointer\"\r\n              onClick={item.onClick}\r\n            >\r\n              <Link\r\n                href={item.url}\r\n                className=\"grid grid-cols-[40px_1fr] items-center text-gray-200\"\r\n              >\r\n                <span className=\"text-lg text-[#71717a]\">{item.icon}</span>\r\n                <span className=\"ml-2\">{item.name}</span>\r\n              </Link>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </nav>\r\n\r\n      <div className=\"h-6 w-6 text-center font-extrabold rounded-full hover:bg-[#ffffff33] m-2 cursor-pointer\">\r\n        <button\r\n          onClick={closeModal}\r\n          className=\"h-4 w-4 text-red-500 cursor-pointer\"\r\n        >\r\n          {cross}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AASA,MAAM,eAAe;IACnB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,cAAc,EAAE,GAC7D,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAClB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IACtC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,OAAO;QACX;YACE,MAAM;YACN,KAAK;YACL,MAAM,sHAAA,CAAA,OAAI;YACV,SAAS;gBACP;gBACA,OAAO,IAAI,CAAC;YACd;QACF;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM,sHAAA,CAAA,UAAO;YACb,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA,OAAO,IAAI,CAAC;YACd;QACF;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BACC,cAAA,8OAAC;oBAAG,WAAU;8BACX,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;4BAEC,WAAU;4BACV,SAAS,KAAK,OAAO;sCAErB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,GAAG;gCACd,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAA0B,KAAK,IAAI;;;;;;kDACnD,8OAAC;wCAAK,WAAU;kDAAQ,KAAK,IAAI;;;;;;;;;;;;2BAT9B;;;;;;;;;;;;;;;0BAgBb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAET,sHAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;AAKhB;uCAEe", "debugId": null}}, {"offset": {"line": 2403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/modals/EmailVerificationModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nimport { useUserContext } from \"@/context/userContext\";\r\nimport React, { useState } from \"react\";\r\n\r\nconst EmailVerificationModal = () => {\r\n  const { closeModal } = useGlobalContext();\r\n  const { emailVerification } = useUserContext();\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleEmailVerification = async () => {\r\n    setLoading(true);\r\n    await emailVerification();\r\n    setLoading(false);\r\n    closeModal();\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed top-0 right-0 z-40 h-full w-full bg-[#000]/30 backdrop-blur-sm bg-opacity-50 flex items-center justify-center\">\r\n      <div className=\"bg-[#252525] rounded-lg p-8 max-w-md w-full shadow-lg border border-yellow-500/30\">\r\n        <h2 className=\"text-yellow-400 text-2xl font-bold mb-4\">\r\n          Email Not Verified\r\n        </h2>\r\n        <p className=\"text-gray-200 mb-6\">\r\n          Your email is not verified. Please verify your email to create\r\n          snippets.\r\n        </p>\r\n        <div className=\"flex gap-4 justify-end\">\r\n          <button\r\n            className=\"px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition\"\r\n            onClick={handleEmailVerification}\r\n            disabled={loading}\r\n          >\r\n            {loading ? \"Sending...\" : \"Send Verification Email\"}\r\n          </button>\r\n          <button\r\n            className=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition\"\r\n            onClick={closeModal}\r\n          >\r\n            Close\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EmailVerificationModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKA,MAAM,yBAAyB;IAC7B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IACtC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,0BAA0B;QAC9B,WAAW;QACX,MAAM;QACN,WAAW;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0C;;;;;;8BAGxD,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAIlC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,UAAU,eAAe;;;;;;sCAE5B,8OAAC;4BACC,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 2493, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/providers/ModalProvider.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useGlobalContext } from \"@/context/globalContext\";\r\nimport AddSnippetModal from \"@/components/modals/addSnippetModal\";\r\nimport React from \"react\";\r\nimport ProfileModal from \"@/components/modals/profileModal\";\r\nimport EmailVerificationModal from \"@/components/modals/EmailVerificationModal\";\r\n\r\nconst ModalProvider = () => {\r\n  const { modalMode, isEditing } = useGlobalContext();\r\n\r\n  return (\r\n    <div>\r\n      {isEditing && <AddSnippetModal />}\r\n      {modalMode === \"profile\" && <ProfileModal />}\r\n      {modalMode === \"email-verification\" && <EmailVerificationModal />}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ModalProvider;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AALA;;;;;;AAOA,MAAM,gBAAgB;IACpB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD;IAEhD,qBACE,8OAAC;;YACE,2BAAa,8OAAC,+IAAA,CAAA,UAAe;;;;;YAC7B,cAAc,2BAAa,8OAAC,4IAAA,CAAA,UAAY;;;;;YACxC,cAAc,sCAAwB,8OAAC,sJAAA,CAAA,UAAsB;;;;;;;;;;;AAGpE;uCAEe", "debugId": null}}]}