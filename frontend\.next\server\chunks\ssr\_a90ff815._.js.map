{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/auth/LoginForm/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/auth/LoginForm/LoginForm.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/LoginForm/LoginForm.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/auth/LoginForm/LoginForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/auth/LoginForm/LoginForm.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/LoginForm/LoginForm.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/app/login/page.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport LoginForm from \"@/components/auth/LoginForm/LoginForm\";\r\nimport Image from \"next/image\";\r\n\r\nconst page = () => {\r\n  return (\r\n    <div className=\"h-[93vh] w-full flex items-center justify-center\">\r\n      <div className=\"w-[80%] h-[90%] flex flex-col md:flex-row bg-[#181818]\">\r\n        {/* Left Side */}\r\n        <div className=\"flex-1 min-h-full flex flex-col justify-center items-center relative overflow-hidden bg-gradient-to-br from-[#232526] to-[#181818] rounded-none md:rounded-tl-3xl md:rounded-bl-3xl\">\r\n          {/* Optional: Glass background image overlay */}\r\n          <Image\r\n            src=\"/glass-bg.png\"\r\n            alt=\"background glass\"\r\n            fill\r\n            className=\"object-cover opacity-20 pointer-events-none select-none\"\r\n          />\r\n          <div className=\"relative z-10 w-[80%] max-w-xl text-center p-8 rounded-2xl backdrop-blur-md bg-white/10 border border-white/20 shadow-xl flex flex-col justify-center items-center\">\r\n            <h1 className=\"text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg\">\r\n              Unlock, Share, and Discover Code Snippets Effortlessly\r\n            </h1>\r\n            <p className=\"text-lg md:text-xl text-gray-200 mb-2\">\r\n              Join the{\" \"}\r\n              <span className=\"text-[#6EE7B7] font-semibold\">Snippy</span>{\" \"}\r\n              community and supercharge your coding journey!\r\n            </p>\r\n          </div>\r\n        </div>\r\n        {/* Right Side */}\r\n        <div className=\"flex-1 min-h-full flex items-center justify-center bg-[#181818] p-0\">\r\n          <div className=\"w-full h-full flex flex-col justify-center items-center bg-[#232526] border border-white/10 rounded-none md:rounded-tr-3xl md:rounded-br-3xl shadow-2xl\">\r\n            <h2 className=\"text-3xl font-bold mb-2 text-white text-center\">\r\n              Welcome Back\r\n            </h2>\r\n            <p className=\"mb-8 text-gray-400 text-center\">\r\n              Log in to your account to continue\r\n            </p>\r\n            <div className=\"w-full flex justify-center items-center\">\r\n              <LoginForm />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,OAAO;IACX,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,IAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;;wCAAwC;wCAC1C;sDACT,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;wCAAc;wCAAI;;;;;;;;;;;;;;;;;;;8BAMvE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAG9C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oJAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe", "debugId": null}}]}