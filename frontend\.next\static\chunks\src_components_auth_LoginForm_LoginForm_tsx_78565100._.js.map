{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Snippy/frontend/src/components/auth/LoginForm/LoginForm.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useUserContext } from \"@/context/userContext\";\r\nimport React from \"react\";\r\nfunction LoginForm() {\r\n  const { loginUser, userState, handlerUserInput } = useUserContext();\r\n  const { email, password } = userState;\r\n  const [showPassword, setShowPassword] = React.useState(false);\r\n\r\n  const togglePassword = () => setShowPassword(!showPassword);\r\n\r\n  return (\r\n    <form\r\n      className=\"relative w-full max-w-[420px] px-6 py-10 rounded-2xl bg-[#232526]/80 backdrop-blur-md border border-white/10 shadow-xl\"\r\n      style={{ boxShadow: \"0 8px 32px 0 rgba(31, 38, 135, 0.25)\" }}\r\n    >\r\n      <div className=\"relative z-10\">\r\n        <h1 className=\"mb-2 text-center text-2xl font-bold text-white tracking-wide\">\r\n          Login to Your Account\r\n        </h1>\r\n        <p className=\"mb-8 px-4 text-center text-gray-400 text-[15px]\">\r\n          Login now. Don&apos;t have an account?{\" \"}\r\n          <a\r\n            href=\"/register\"\r\n            className=\"font-bold text-[#6EE7B7] hover:text-[#2ECC71] transition-all duration-300\"\r\n          >\r\n            Register here\r\n          </a>\r\n        </p>\r\n\r\n        <div className=\"mt-4 flex flex-col\">\r\n          <label htmlFor=\"email\" className=\"mb-1 text-gray-300 font-medium\">\r\n            Email\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"email\"\r\n            value={email}\r\n            onChange={(e) => handlerUserInput(\"email\")(e)}\r\n            name=\"email\"\r\n            className=\"px-4 py-3 bg-[#181818] border border-white/10 rounded-lg outline-none text-white placeholder-gray-500 focus:ring-2 focus:ring-[#6EE7B7] transition-all\"\r\n            placeholder=\"<EMAIL>\"\r\n            autoComplete=\"email\"\r\n          />\r\n        </div>\r\n        <div className=\"relative mt-4 flex flex-col\">\r\n          <label htmlFor=\"password\" className=\"mb-1 text-gray-300 font-medium\">\r\n            Password\r\n          </label>\r\n          <input\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            id=\"password\"\r\n            value={password}\r\n            onChange={(e) => handlerUserInput(\"password\")(e)}\r\n            name=\"password\"\r\n            className=\"px-4 py-3 bg-[#181818] border border-white/10 rounded-lg outline-none text-white placeholder-gray-500 focus:ring-2 focus:ring-[#6EE7B7] transition-all\"\r\n            placeholder=\"***************\"\r\n            autoComplete=\"current-password\"\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            className=\"absolute p-1 right-4 top-[43%] text-[20px] text-gray-400 hover:text-[#6EE7B7] transition-colors\"\r\n            tabIndex={-1}\r\n          >\r\n            {showPassword ? (\r\n              <i className=\"fas fa-eye-slash\" onClick={togglePassword}></i>\r\n            ) : (\r\n              <i className=\"fas fa-eye\" onClick={togglePassword}></i>\r\n            )}\r\n          </button>\r\n        </div>\r\n        <div className=\"mt-4 flex justify-end\">\r\n          <a\r\n            href=\"/forgot-password\"\r\n            className=\"font-bold text-[#6EE7B7] text-[14px] hover:text-[#2ECC71] transition-all duration-300\"\r\n          >\r\n            Forgot password?\r\n          </a>\r\n        </div>\r\n        <div className=\"flex\">\r\n          <button\r\n            type=\"submit\"\r\n            disabled={!email || !password}\r\n            onClick={loginUser}\r\n            className=\"mt-6 flex-1 px-4 py-3 font-bold bg-[#6EE7B7] text-[#181818] rounded-lg hover:bg-[#2ECC71] transition-colors disabled:opacity-60 disabled:cursor-not-allowed shadow-md\"\r\n          >\r\n            Login Now\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  );\r\n}\r\n\r\nexport default LoginForm;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAGA,SAAS;;IACP,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;IAChE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEvD,MAAM,iBAAiB,IAAM,gBAAgB,CAAC;IAE9C,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,WAAW;QAAuC;kBAE3D,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA+D;;;;;;8BAG7E,6LAAC;oBAAE,WAAU;;wBAAkD;wBACtB;sCACvC,6LAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAAiC;;;;;;sCAGlE,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,iBAAiB,SAAS;4BAC3C,MAAK;4BACL,WAAU;4BACV,aAAY;4BACZ,cAAa;;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,SAAQ;4BAAW,WAAU;sCAAiC;;;;;;sCAGrE,6LAAC;4BACC,MAAM,eAAe,SAAS;4BAC9B,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,iBAAiB,YAAY;4BAC9C,MAAK;4BACL,WAAU;4BACV,aAAY;4BACZ,cAAa;;;;;;sCAEf,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,UAAU,CAAC;sCAEV,6BACC,6LAAC;gCAAE,WAAU;gCAAmB,SAAS;;;;;qDAEzC,6LAAC;gCAAE,WAAU;gCAAa,SAAS;;;;;;;;;;;;;;;;;8BAIzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,UAAU,CAAC,SAAS,CAAC;wBACrB,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxFS;;QAC4C,gIAAA,CAAA,iBAAc;;;KAD1D;uCA0FM", "debugId": null}}]}